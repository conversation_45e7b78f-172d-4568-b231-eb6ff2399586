{"roots": ["unclara_vpn_app"], "packages": [{"name": "unclara_vpn_app", "version": "1.0.0+1", "dependencies": ["connectivity_plus", "crypto", "cupertino_icons", "dio", "fl_chart", "flutter", "flutter_background_service", "flutter_riverpod", "flutter_svg", "hive", "hive_flutter", "http", "intl", "local_auth", "lottie", "material_design_icons_flutter", "package_info_plus", "provider", "riverpod", "shared_preferences", "shimmer", "url_launcher", "workmanager"], "devDependencies": ["build_runner", "flutter_lints", "flutter_test", "hive_generator"]}, {"name": "build_runner", "version": "2.4.13", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "build_resolvers", "build_runner_core", "code_builder", "collection", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http_multi_server", "io", "js", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web_socket_channel", "yaml"]}, {"name": "hive_generator", "version": "2.0.1", "dependencies": ["analyzer", "build", "hive", "source_gen", "source_helper"]}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["clock", "collection", "fake_async", "flutter", "leak_tracker_flutter_testing", "matcher", "meta", "path", "stack_trace", "stream_channel", "test_api", "vector_math"]}, {"name": "package_info_plus", "version": "8.3.1", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "url_launcher", "version": "6.3.2", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "intl", "version": "0.19.0", "dependencies": ["clock", "meta", "path"]}, {"name": "shimmer", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "fl_chart", "version": "0.69.2", "dependencies": ["equatable", "flutter"]}, {"name": "flutter_background_service", "version": "5.1.0", "dependencies": ["flutter", "flutter_background_service_android", "flutter_background_service_ios", "flutter_background_service_platform_interface"]}, {"name": "workmanager", "version": "0.5.2", "dependencies": ["flutter"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "local_auth", "version": "2.3.0", "dependencies": ["flutter", "local_auth_android", "local_auth_darwin", "local_auth_platform_interface", "local_auth_windows"]}, {"name": "hive_flutter", "version": "1.1.0", "dependencies": ["flutter", "hive", "path", "path_provider"]}, {"name": "hive", "version": "2.2.3", "dependencies": ["crypto", "meta"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "http", "version": "1.5.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "connectivity_plus", "version": "6.1.5", "dependencies": ["collection", "connectivity_plus_platform_interface", "flutter", "flutter_web_plugins", "meta", "nm", "web"]}, {"name": "dio", "version": "5.9.0", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "mime", "path"]}, {"name": "flutter_riverpod", "version": "2.6.1", "dependencies": ["collection", "flutter", "meta", "riverpod", "state_notifier"]}, {"name": "riverpod", "version": "2.6.1", "dependencies": ["collection", "meta", "stack_trace", "state_notifier"]}, {"name": "provider", "version": "6.1.5+1", "dependencies": ["collection", "flutter", "nested"]}, {"name": "lottie", "version": "3.3.2", "dependencies": ["archive", "flutter", "http", "path", "vector_math"]}, {"name": "flutter_svg", "version": "2.2.1", "dependencies": ["flutter", "http", "vector_graphics", "vector_graphics_codec", "vector_graphics_compiler"]}, {"name": "material_design_icons_flutter", "version": "7.0.7296", "dependencies": ["flutter"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "watcher", "version": "1.1.3", "dependencies": ["async", "path"]}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "shelf_web_socket", "version": "2.0.1", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "pool", "version": "1.5.2", "dependencies": ["async", "stack_trace"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "js", "version": "0.7.2", "dependencies": []}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "dart_style", "version": "2.3.6", "dependencies": ["analyzer", "args", "collection", "path", "pub_semver", "source_span"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "code_builder", "version": "4.11.0", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "build_runner_core", "version": "7.3.2", "dependencies": ["async", "build", "build_config", "build_resolvers", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "build_resolvers", "version": "2.4.2", "dependencies": ["analyzer", "async", "build", "collection", "convert", "crypto", "graphs", "logging", "package_config", "path", "pool", "pub_semver", "stream_transform", "yaml"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "build_config", "version": "1.1.2", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse", "yaml"]}, {"name": "build", "version": "2.4.1", "dependencies": ["analyzer", "async", "convert", "crypto", "glob", "logging", "meta", "package_config", "path"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "analyzer", "version": "6.4.1", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "source_helper", "version": "1.3.5", "dependencies": ["analyzer", "collection", "source_gen"]}, {"name": "source_gen", "version": "1.5.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "source_span", "yaml"]}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.10", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.2.0", "dependencies": []}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.6", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "package_info_plus_platform_interface", "version": "3.2.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["flutter"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_macos", "version": "3.2.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.22", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "flutter_background_service_ios", "version": "5.0.3", "dependencies": ["flutter", "flutter_background_service_platform_interface"]}, {"name": "flutter_background_service_android", "version": "6.3.1", "dependencies": ["flutter", "flutter_background_service_platform_interface"]}, {"name": "flutter_background_service_platform_interface", "version": "5.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "local_auth_windows", "version": "1.0.11", "dependencies": ["flutter", "local_auth_platform_interface"]}, {"name": "local_auth_platform_interface", "version": "1.1.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "local_auth_darwin", "version": "1.6.1", "dependencies": ["flutter", "intl", "local_auth_platform_interface"]}, {"name": "local_auth_android", "version": "1.0.53", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "intl", "local_auth_platform_interface"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.13", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "nm", "version": "0.5.0", "dependencies": ["dbus"]}, {"name": "connectivity_plus_platform_interface", "version": "2.0.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "state_notifier", "version": "1.0.0", "dependencies": ["meta"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "vector_graphics_compiler", "version": "1.1.19", "dependencies": ["args", "meta", "path", "path_parsing", "vector_graphics_codec", "xml"]}, {"name": "vector_graphics_codec", "version": "1.1.13", "dependencies": []}, {"name": "vector_graphics", "version": "1.1.19", "dependencies": ["flutter", "http", "vector_graphics_codec"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "built_value", "version": "8.12.0", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "_fe_analyzer_shared", "version": "67.0.0", "dependencies": ["meta"]}, {"name": "leak_tracker_testing", "version": "3.0.2", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "11.0.2", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.30", "dependencies": ["flutter"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.2", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.18", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "posix", "version": "6.0.3", "dependencies": ["ffi", "meta", "path"]}, {"name": "xml", "version": "6.6.1", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "path_parsing", "version": "1.1.0", "dependencies": ["meta", "vector_math"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "vm_service", "version": "15.0.2", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "petitparser", "version": "7.0.1", "dependencies": ["collection", "meta"]}], "configVersion": 1}