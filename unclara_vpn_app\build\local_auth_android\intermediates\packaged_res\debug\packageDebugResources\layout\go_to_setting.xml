<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="vertical"
    android:paddingLeft="24dp"
    android:paddingRight="24dp">

    <TextView
        android:id="@+id/fingerprint_required"
        style="@android:style/TextAppearance.DeviceDefault.Medium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:paddingTop="24dp"
        android:paddingBottom="20dp"
        android:textColor="@color/black_text"
        android:textIsSelectable="false"
        android:textSize="@dimen/huge_text_size" />

    <TextView
        android:id="@+id/go_to_setting_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingBottom="28dp"
        android:textColor="@color/grey_text"
        android:textIsSelectable="false"
        android:textSize="@dimen/medium_text_size"
        android:textStyle="normal" />
</LinearLayout>
