<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":workmanager" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\unclara-backend\vpn-app\unclara_vpn_app\build\workmanager\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\unclara-backend\vpn-app\unclara_vpn_app\build\path_provider_android\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\unclara-backend\vpn-app\unclara_vpn_app\build\flutter_plugin_android_lifecycle\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":connectivity_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\unclara-backend\vpn-app\unclara_vpn_app\build\connectivity_plus\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":url_launcher_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\unclara-backend\vpn-app\unclara_vpn_app\build\url_launcher_android\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":local_auth_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\unclara-backend\vpn-app\unclara_vpn_app\build\local_auth_android\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":flutter_background_service_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\unclara-backend\vpn-app\unclara_vpn_app\build\flutter_background_service_android\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\unclara-backend\vpn-app\unclara_vpn_app\build\shared_preferences_android\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":package_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\unclara-backend\vpn-app\unclara_vpn_app\build\package_info_plus\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\unclara-backend\vpn-app\unclara_vpn_app\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\unclara-backend\vpn-app\unclara_vpn_app\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\unclara-backend\vpn-app\unclara_vpn_app\build\app\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>