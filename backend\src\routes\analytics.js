import express from 'express';
import { query, body } from 'express-validator';
import { protect } from '../middleware/auth.js';
import AnalyticsService from '../services/AnalyticsService.js';
import { validationResult } from 'express-validator';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Mock analytics data
const mockAnalytics = {
  dashboard: {
    today: {
      blockedAttempts: 12,
      timeSpent: 45, // minutes
      sitesBlocked: 8,
      goalProgress: 0.7
    },
    week: {
      blockedAttempts: 89,
      timeSpent: 320, // minutes
      sitesBlocked: 25,
      averageDaily: 64
    },
    month: {
      blockedAttempts: 356,
      timeSpent: 1280, // minutes
      sitesBlocked: 78,
      averageDaily: 45
    }
  },
  usageHistory: [
    { date: '2024-01-01', blockedAttempts: 15, timeSpent: 60 },
    { date: '2024-01-02', blockedAttempts: 8, timeSpent: 30 },
    { date: '2024-01-03', blockedAttempts: 12, timeSpent: 45 },
    { date: '2024-01-04', blockedAttempts: 20, timeSpent: 75 },
    { date: '2024-01-05', blockedAttempts: 6, timeSpent: 25 },
    { date: '2024-01-06', blockedAttempts: 18, timeSpent: 55 },
    { date: '2024-01-07', blockedAttempts: 10, timeSpent: 40 }
  ],
  topBlockedSites: [
    { domain: 'facebook.com', attempts: 25, category: 'social_media' },
    { domain: 'instagram.com', attempts: 18, category: 'social_media' },
    { domain: 'holiganbet1978.com', attempts: 15, category: 'gambling' },
    { domain: 'youtube.com', attempts: 12, category: 'entertainment' },
    { domain: 'twitter.com', attempts: 8, category: 'social_media' }
  ],
  categoryStats: [
    { category: 'social_media', attempts: 51, percentage: 45 },
    { category: 'gambling', attempts: 28, percentage: 25 },
    { category: 'entertainment', attempts: 20, percentage: 18 },
    { category: 'gaming', attempts: 14, percentage: 12 }
  ]
};

// @desc    Get dashboard analytics
// @route   GET /api/v1/analytics/dashboard
// @access  Private
router.get('/dashboard',
  [
    query('timeframe')
      .optional()
      .isIn(['1h', '24h', '7d', '30d'])
      .withMessage('Invalid timeframe')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const userId = req.user.id;
      const { timeframe = '24h' } = req.query;

      const analytics = await AnalyticsService.getDashboardAnalytics(userId, timeframe);

      res.status(200).json({
        success: true,
        data: analytics
      });
    } catch (error) {
      console.error('Dashboard Analytics Error:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

// @desc    Get usage history
// @route   GET /api/v1/analytics/usage-history
// @access  Private
router.get('/usage-history',
  [
    query('days')
      .optional()
      .isInt({ min: 1, max: 365 })
      .withMessage('Days must be between 1 and 365'),
    query('granularity')
      .optional()
      .isIn(['hourly', 'daily', 'weekly'])
      .withMessage('Invalid granularity')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const userId = req.user.id;
      const { days = 7, granularity = 'daily' } = req.query;

      const history = await AnalyticsService.getUsageHistory(userId, parseInt(days), granularity);

      res.status(200).json({
        success: true,
        data: {
          days: parseInt(days),
          granularity,
          history
        }
      });
    } catch (error) {
      console.error('Usage History Error:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

// @desc    Get blocked attempts analysis
// @route   GET /api/v1/analytics/blocked-attempts
// @access  Private
router.get('/blocked-attempts',
  [
    query('days')
      .optional()
      .isInt({ min: 1, max: 365 })
      .withMessage('Days must be between 1 and 365')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const userId = req.user.id;
      const { days = 7 } = req.query;

      const analysis = await AnalyticsService.getBlockedAttemptsAnalysis(userId, parseInt(days));

      res.status(200).json({
        success: true,
        data: analysis
      });
    } catch (error) {
      console.error('Blocked Attempts Analysis Error:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

// @desc    Get weekly report
// @route   GET /api/v1/analytics/weekly-report
// @access  Private
router.get('/weekly-report', async (req, res) => {
  try {
    const userId = req.user.id;
    const report = await AnalyticsService.getWeeklyReport(userId);

    res.status(200).json({
      success: true,
      data: report
    });
  } catch (error) {
    console.error('Weekly Report Error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// @desc    Get monthly report
// @route   GET /api/v1/analytics/monthly-report
// @access  Private
router.get('/monthly-report', async (req, res) => {
  try {
    const userId = req.user.id;
    const report = await AnalyticsService.getMonthlyReport(userId);

    res.status(200).json({
      success: true,
      data: report
    });
  } catch (error) {
    console.error('Monthly Report Error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// @desc    Log usage event
// @route   POST /api/v1/analytics/usage-log
// @access  Private
router.post('/usage-log', async (req, res) => {
  try {
    const { domain, action, duration } = req.body;

    res.status(201).json({
      success: true,
      message: 'Usage logged successfully',
      data: {
        domain,
        action,
        duration,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// @desc    Log blocked attempt
// @route   POST /api/v1/analytics/blocked-attempt
// @access  Private
router.post('/blocked-attempt', async (req, res) => {
  try {
    const { domain, category, userAgent } = req.body;

    res.status(201).json({
      success: true,
      message: 'Blocked attempt logged',
      data: {
        domain,
        category,
        userAgent,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;
